'use client';

import { useState, useEffect, useRef } from 'react';
import { SearchParams, SITE_OPTIONS, FILETYPE_OPTIONS } from '@/types/search';

interface SearchFiltersProps {
  searchParams: SearchParams;
  onParamsChange: (params: SearchParams) => void;
  onSearch: (params: SearchParams) => void;
}

export default function SearchFilters({ searchParams, onParamsChange, onSearch }: SearchFiltersProps) {
  const [showSites, setShowSites] = useState(false);
  const [showFileTypes, setShowFileTypes] = useState(false);
  const sitesRef = useRef<HTMLDivElement>(null);
  const fileTypesRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sitesRef.current && !sitesRef.current.contains(event.target as Node)) {
        setShowSites(false);
      }
      if (fileTypesRef.current && !fileTypesRef.current.contains(event.target as Node)) {
        setShowFileTypes(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleSite = (siteId: string) => {
    const newSites = searchParams.sites.includes(siteId)
      ? searchParams.sites.filter(s => s !== siteId)
      : [...searchParams.sites, siteId];
    
    const newParams = { ...searchParams, sites: newSites, page: 1 };
    onParamsChange(newParams);
  };

  const toggleFileType = (typeId: string) => {
    const newTypes = searchParams.filetypes.includes(typeId)
      ? searchParams.filetypes.filter(t => t !== typeId)
      : [...searchParams.filetypes, typeId];
    
    const newParams = { ...searchParams, filetypes: newTypes, page: 1 };
    onParamsChange(newParams);
  };

  const clearAllFilters = () => {
    const newParams = { ...searchParams, sites: [], filetypes: [], page: 1 };
    onParamsChange(newParams);
  };

  const applyFilters = () => {
    onSearch(searchParams);
  };

  return (
    <div className="mb-8 space-y-4">
      {/* Filter Pills */}
      <div className="flex flex-wrap gap-3 items-center">
        {/* File Types */}
        <div className="relative" ref={fileTypesRef}>
          <button
            onClick={() => setShowFileTypes(!showFileTypes)}
            className="
              px-4 py-2 rounded-full border border-border bg-card
              hover:bg-accent hover:text-accent-foreground
              transition-all duration-200 flex items-center gap-2
            "
          >
            📁 文件类型
            {searchParams.filetypes.length > 0 && (
              <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                {searchParams.filetypes.length}
              </span>
            )}
            <span className={`transition-transform ${showFileTypes ? 'rotate-180' : ''}`}>▼</span>
          </button>
          
          {showFileTypes && (
            <div className="absolute top-full left-0 mt-2 p-4 bg-card border border-border rounded-lg shadow-lg z-10 min-w-64">
              <div className="grid grid-cols-2 gap-2">
                {FILETYPE_OPTIONS.map((type) => (
                  <label key={type.id} className="flex items-center gap-2 cursor-pointer hover:bg-accent p-2 rounded">
                    <input
                      type="checkbox"
                      checked={searchParams.filetypes.includes(type.id)}
                      onChange={() => toggleFileType(type.id)}
                      className="rounded"
                    />
                    <span>{type.icon}</span>
                    <span className="text-sm">{type.name}</span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Sites */}
        <div className="relative" ref={sitesRef}>
          <button
            onClick={() => setShowSites(!showSites)}
            className="
              px-4 py-2 rounded-full border border-border bg-card
              hover:bg-accent hover:text-accent-foreground
              transition-all duration-200 flex items-center gap-2
            "
          >
            🌐 来源站点
            {searchParams.sites.length > 0 && (
              <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                {searchParams.sites.length}
              </span>
            )}
            <span className={`transition-transform ${showSites ? 'rotate-180' : ''}`}>▼</span>
          </button>
          
          {showSites && (
            <div className="absolute top-full left-0 mt-2 p-4 bg-card border border-border rounded-lg shadow-lg z-10 min-w-64">
              <div className="space-y-2">
                {SITE_OPTIONS.map((site) => (
                  <label key={site.id} className="flex items-center gap-2 cursor-pointer hover:bg-accent p-2 rounded">
                    <input
                      type="checkbox"
                      checked={searchParams.sites.includes(site.id)}
                      onChange={() => toggleSite(site.id)}
                      className="rounded"
                    />
                    <span className="text-sm">{site.name}</span>
                    <span className="text-xs text-muted-foreground">({site.domain})</span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Sort */}
        <select
          value={searchParams.sort}
          onChange={(e) => onParamsChange({ ...searchParams, sort: e.target.value as 'relevance' | 'time' })}
          className="px-4 py-2 rounded-full border border-border bg-card hover:bg-accent"
        >
          <option value="relevance">📊 相关度</option>
          <option value="time">🕒 时间</option>
        </select>

        {/* Clear Filters */}
        {(searchParams.sites.length > 0 || searchParams.filetypes.length > 0) && (
          <button
            onClick={clearAllFilters}
            className="px-4 py-2 rounded-full border border-border bg-card hover:bg-destructive hover:text-destructive-foreground transition-all duration-200"
          >
            ✕ 清除筛选
          </button>
        )}

        {/* Apply Filters */}
        <button
          onClick={applyFilters}
          className="px-6 py-2 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-200"
        >
          应用筛选
        </button>
      </div>

      {/* Active Filters Display */}
      {(searchParams.sites.length > 0 || searchParams.filetypes.length > 0) && (
        <div className="flex flex-wrap gap-2">
          {searchParams.filetypes.map((typeId) => {
            const type = FILETYPE_OPTIONS.find(t => t.id === typeId);
            return type ? (
              <span key={typeId} className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm flex items-center gap-1">
                {type.icon} {type.name}
                <button onClick={() => toggleFileType(typeId)} className="ml-1 hover:text-primary/70">✕</button>
              </span>
            ) : null;
          })}
          {searchParams.sites.map((siteId) => {
            const site = SITE_OPTIONS.find(s => s.id === siteId);
            return site ? (
              <span key={siteId} className="px-3 py-1 bg-secondary/10 text-secondary rounded-full text-sm flex items-center gap-1">
                🌐 {site.name}
                <button onClick={() => toggleSite(siteId)} className="ml-1 hover:text-secondary/70">✕</button>
              </span>
            ) : null;
          })}
        </div>
      )}
    </div>
  );
}
