'use client';

import { SearchResult } from '@/types/search';

interface ResultCardProps {
  result: SearchResult;
  selected: boolean;
  onSelect: (selected: boolean) => void;
}

export default function ResultCard({ result, selected, onSelect }: ResultCardProps) {
  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'pdf': return '📄';
      case 'doc':
      case 'docx': return '📝';
      case 'ppt':
      case 'pptx': return '📊';
      case 'article': return '📰';
      default: return '📄';
    }
  };

  const getFileTypeLabel = (fileType: string) => {
    switch (fileType) {
      case 'pdf': return 'PDF';
      case 'doc':
      case 'docx': return 'Word';
      case 'ppt':
      case 'pptx': return 'PPT';
      case 'article': return '文章';
      default: return fileType.toUpperCase();
    }
  };

  const handleDownload = async () => {
    if (result.directDownloadUrl) {
      // Direct download
      window.open(result.directDownloadUrl, '_blank');
    } else {
      // Proxy download through our API
      const downloadUrl = `/api/download?url=${encodeURIComponent(result.url)}&filename=${encodeURIComponent(result.title)}`;
      window.open(downloadUrl, '_blank');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className={`
      bg-card border border-border rounded-lg p-6 transition-all duration-200
      hover:shadow-lg hover:border-primary/20
      ${selected ? 'ring-2 ring-primary/50 bg-primary/5' : ''}
    `}>
      <div className="flex gap-4">
        {/* Selection Checkbox */}
        <div className="flex-shrink-0 pt-1">
          <input
            type="checkbox"
            checked={selected}
            onChange={(e) => onSelect(e.target.checked)}
            className="rounded w-4 h-4"
          />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* File Type Badge */}
          <div className="flex items-center gap-2 mb-3">
            <span className={`
              inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium
              ${result.fileType === 'pdf' ? 'bg-red-100 text-red-700' : ''}
              ${result.fileType === 'docx' || result.fileType === 'doc' ? 'bg-blue-100 text-blue-700' : ''}
              ${result.fileType === 'pptx' || result.fileType === 'ppt' ? 'bg-orange-100 text-orange-700' : ''}
              ${result.fileType === 'article' ? 'bg-green-100 text-green-700' : ''}
            `}>
              {getFileTypeIcon(result.fileType)}
              {getFileTypeLabel(result.fileType)}
            </span>
            {result.fileSize && (
              <span className="text-xs text-muted-foreground">
                {result.fileSize}
              </span>
            )}
          </div>

          {/* Title */}
          <h3 className="text-lg font-semibold mb-2 line-clamp-2 hover:text-primary cursor-pointer">
            <a href={result.url} target="_blank" rel="noopener noreferrer">
              {result.title}
            </a>
          </h3>

          {/* Source and Date */}
          <div className="flex items-center gap-3 mb-3 text-sm text-muted-foreground">
            <span className="flex items-center gap-1">
              🏢 {result.sourceName}
            </span>
            <span className="flex items-center gap-1">
              📅 {formatDate(result.publishedAt)}
            </span>
          </div>

          {/* Snippet */}
          <p className="text-muted-foreground text-sm line-clamp-2 mb-4">
            {result.snippet}
          </p>

          {/* Actions */}
          <div className="flex items-center gap-3">
            <button
              onClick={handleDownload}
              className="
                px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm
                hover:bg-primary/90 transition-all duration-200
                flex items-center gap-2
              "
            >
              ⬇️ 下载
            </button>
            
            <a
              href={result.url}
              target="_blank"
              rel="noopener noreferrer"
              className="
                px-4 py-2 border border-border rounded-lg text-sm
                hover:bg-accent hover:text-accent-foreground transition-all duration-200
                flex items-center gap-2
              "
            >
              🔗 查看原文
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
