'use client';

import { useState } from 'react';
import { useTheme } from './ThemeProvider';
import SearchHeader from './SearchHeader';
import SearchFilters from './SearchFilters';
import SearchResults from './SearchResults';
import { SearchParams, SearchResult } from '@/types/search';

export default function SearchInterface() {
  const { theme } = useTheme();
  const [searchParams, setSearchParams] = useState<SearchParams>({
    query: '',
    sites: [],
    filetypes: [],
    page: 1,
    pageSize: 20,
    sort: 'relevance'
  });
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedResults, setSelectedResults] = useState<Set<string>>(new Set());

  const handleSearch = async (params: SearchParams) => {
    setLoading(true);
    setSearchParams(params);

    try {
      const queryParams = new URLSearchParams({
        query: params.query,
        sites: params.sites.join(','),
        filetypes: params.filetypes.join(','),
        page: params.page.toString(),
        pageSize: params.pageSize.toString(),
        sort: params.sort
      });

      const response = await fetch(`/api/search?${queryParams}`);

      if (!response.ok) {
        throw new Error(`Search failed: ${response.status}`);
      }

      const data = await response.json();
      setResults(data.results || []);
    } catch (error) {
      console.error('Search failed:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectResult = (id: string, selected: boolean) => {
    const newSelected = new Set(selectedResults);
    if (selected) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedResults(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedResults.size === results.length) {
      setSelectedResults(new Set());
    } else {
      setSelectedResults(new Set(results.map(r => r.id)));
    }
  };

  const handleBatchDownload = async () => {
    const selectedItems = results.filter(r => selectedResults.has(r.id));

    if (selectedItems.length === 0) {
      alert('请先选择要下载的文档');
      return;
    }

    try {
      const downloadItems = selectedItems
        .filter(item => item.directDownloadUrl) // Only download items with direct URLs
        .map(item => ({
          url: item.directDownloadUrl!,
          filename: `${item.title}.${item.fileType}`
        }));

      if (downloadItems.length === 0) {
        alert('所选文档暂不支持批量下载');
        return;
      }

      const response = await fetch('/api/batch-download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ items: downloadItems })
      });

      if (!response.ok) {
        throw new Error('批量下载失败');
      }

      // Download the zip file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'documents.zip';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Clear selection
      setSelectedResults(new Set());
    } catch (error) {
      console.error('Batch download failed:', error);
      alert('批量下载失败，请稍后重试');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <SearchHeader onSearch={handleSearch} />
      
      <SearchFilters 
        searchParams={searchParams}
        onParamsChange={setSearchParams}
        onSearch={handleSearch}
      />
      
      <SearchResults
        results={results}
        loading={loading}
        selectedResults={selectedResults}
        onSelectResult={handleSelectResult}
        onSelectAll={handleSelectAll}
        onBatchDownload={handleBatchDownload}
      />
    </div>
  );
}
