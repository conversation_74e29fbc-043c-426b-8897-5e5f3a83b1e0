'use client';

import { useState } from 'react';
import { useTheme } from './ThemeProvider';
import { SearchParams } from '@/types/search';

interface SearchHeaderProps {
  onSearch: (params: SearchParams) => void;
}

export default function SearchHeader({ onSearch }: SearchHeaderProps) {
  const { theme, setTheme } = useTheme();
  const [query, setQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch({
        query: query.trim(),
        sites: [],
        filetypes: [],
        page: 1,
        pageSize: 20,
        sort: 'relevance'
      });
    }
  };

  const themes = [
    { id: 'light', name: '简洁', icon: '☀️' },
    { id: 'cream', name: '奶油', icon: '🌙' },
    { id: 'glass', name: '玻璃', icon: '💎' },
    { id: 'dark', name: '深色', icon: '🌚' },
    { id: 'brand', name: '品牌', icon: '🎨' },
  ];

  return (
    <header className="text-center mb-12">
      {/* Theme Switcher */}
      <div className="absolute top-4 right-4 flex gap-2">
        {themes.map((t) => (
          <button
            key={t.id}
            onClick={() => setTheme(t.id as any)}
            className={`
              px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
              ${theme === t.id 
                ? 'bg-primary text-primary-foreground shadow-lg' 
                : 'bg-card hover:bg-accent text-card-foreground hover:text-accent-foreground'
              }
            `}
            title={t.name}
          >
            <span className="mr-1">{t.icon}</span>
            {t.name}
          </button>
        ))}
      </div>

      {/* Logo and Title */}
      <div className="mb-8">
        <h1 className="text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-4">
          Fileso
        </h1>
        <p className="text-xl text-muted-foreground">
          美观、智能的文档搜索与下载工具
        </p>
      </div>

      {/* Search Bar */}
      <form onSubmit={handleSubmit} className="max-w-2xl mx-auto">
        <div className="relative group">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="搜索文档、论文、报告..."
            className="
              w-full px-6 py-4 text-lg rounded-2xl border-2 border-border
              bg-card/50 backdrop-blur-sm
              focus:border-primary focus:outline-none focus:ring-4 focus:ring-primary/20
              transition-all duration-200
              placeholder:text-muted-foreground
            "
          />
          <button
            type="submit"
            className="
              absolute right-2 top-1/2 -translate-y-1/2
              px-6 py-2 bg-primary text-primary-foreground
              rounded-xl font-medium
              hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/50
              transition-all duration-200
              disabled:opacity-50 disabled:cursor-not-allowed
            "
            disabled={!query.trim()}
          >
            🔍 搜索
          </button>
        </div>
      </form>
    </header>
  );
}
