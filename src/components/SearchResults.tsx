'use client';

import { SearchResult } from '@/types/search';
import ResultCard from './ResultCard';

interface SearchResultsProps {
  results: SearchResult[];
  loading: boolean;
  selectedResults: Set<string>;
  onSelectResult: (id: string, selected: boolean) => void;
  onSelectAll: () => void;
  onBatchDownload: () => void;
}

export default function SearchResults({
  results,
  loading,
  selectedResults,
  onSelectResult,
  onSelectAll,
  onBatchDownload
}: SearchResultsProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="h-6 bg-muted rounded w-32 animate-pulse"></div>
          <div className="h-10 bg-muted rounded w-24 animate-pulse"></div>
        </div>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-card border border-border rounded-lg p-6 animate-pulse">
            <div className="flex gap-4">
              <div className="w-4 h-4 bg-muted rounded"></div>
              <div className="flex-1 space-y-3">
                <div className="h-6 bg-muted rounded w-3/4"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
                <div className="h-4 bg-muted rounded w-full"></div>
                <div className="h-4 bg-muted rounded w-2/3"></div>
              </div>
              <div className="w-24 h-10 bg-muted rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📭</div>
        <h3 className="text-xl font-semibold mb-2">暂无搜索结果</h3>
        <p className="text-muted-foreground">
          尝试调整搜索关键词或筛选条件
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <p className="text-muted-foreground">
            找到 <span className="font-semibold text-foreground">{results.length}</span> 个结果
          </p>
          
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={selectedResults.size === results.length && results.length > 0}
              onChange={onSelectAll}
              className="rounded"
            />
            <span className="text-sm">全选</span>
          </label>
        </div>

        {selectedResults.size > 0 && (
          <div className="flex items-center gap-3">
            <span className="text-sm text-muted-foreground">
              已选择 {selectedResults.size} 项
            </span>
            <button
              onClick={onBatchDownload}
              className="
                px-4 py-2 bg-primary text-primary-foreground rounded-lg
                hover:bg-primary/90 transition-all duration-200
                flex items-center gap-2
              "
            >
              📦 批量下载
            </button>
          </div>
        )}
      </div>

      {/* Results List */}
      <div className="space-y-4">
        {results.map((result) => (
          <ResultCard
            key={result.id}
            result={result}
            selected={selectedResults.has(result.id)}
            onSelect={(selected) => onSelectResult(result.id, selected)}
          />
        ))}
      </div>

      {/* Pagination Placeholder */}
      <div className="flex justify-center pt-8">
        <div className="flex gap-2">
          <button className="px-4 py-2 border border-border rounded-lg hover:bg-accent transition-all duration-200">
            上一页
          </button>
          <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg">
            1
          </button>
          <button className="px-4 py-2 border border-border rounded-lg hover:bg-accent transition-all duration-200">
            2
          </button>
          <button className="px-4 py-2 border border-border rounded-lg hover:bg-accent transition-all duration-200">
            3
          </button>
          <button className="px-4 py-2 border border-border rounded-lg hover:bg-accent transition-all duration-200">
            下一页
          </button>
        </div>
      </div>
    </div>
  );
}
