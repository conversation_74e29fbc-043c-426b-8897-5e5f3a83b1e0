export interface SearchParams {
  query: string;
  sites: string[];
  filetypes: string[];
  page: number;
  pageSize: number;
  sort: 'relevance' | 'time';
  timeRange?: string;
}

export interface SearchResult {
  id: string;
  title: string;
  url: string;
  sourceName: string;
  sourceIcon?: string;
  snippet: string;
  publishedAt: string;
  fileType: 'pdf' | 'doc' | 'docx' | 'ppt' | 'pptx' | 'article';
  fileSize?: string;
  directDownloadUrl?: string;
  coverImage?: string;
}

export interface SiteOption {
  id: string;
  name: string;
  domain: string;
  icon?: string;
}

export interface FileTypeOption {
  id: string;
  name: string;
  extensions: string[];
  icon: string;
}

export const SITE_OPTIONS: SiteOption[] = [
  { id: 'weixin', name: '微信公众号', domain: 'weixin.qq.com' },
  { id: 'zhihu', name: '知乎', domain: 'zhihu.com' },
  { id: 'zhishixingqiu', name: '知识星球', domain: 'zsxq.com' },
  { id: 'csdn', name: 'CSDN', domain: 'csdn.net' },
  { id: 'jianshu', name: '简书', domain: 'jianshu.com' },
  { id: 'segmentfault', name: 'SegmentFault', domain: 'segmentfault.com' },
  { id: 'juejin', name: '掘金', domain: 'juejin.cn' },
];

export const FILETYPE_OPTIONS: FileTypeOption[] = [
  { id: 'pdf', name: 'PDF', extensions: ['pdf'], icon: '📄' },
  { id: 'word', name: 'Word', extensions: ['doc', 'docx'], icon: '📝' },
  { id: 'powerpoint', name: 'PowerPoint', extensions: ['ppt', 'pptx'], icon: '📊' },
  { id: 'article', name: '文章', extensions: [], icon: '📰' },
];
