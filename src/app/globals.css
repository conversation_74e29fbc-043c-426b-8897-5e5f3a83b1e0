@import "tailwindcss";

/* Base theme variables */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;
}

/* Light theme */
[data-theme="light"] {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --border: #e2e8f0;
}

/* Cream theme */
[data-theme="cream"] {
  --background: #fefcf3;
  --foreground: #3c2e26;
  --card: #faf8f0;
  --card-foreground: #3c2e26;
  --primary: #d97706;
  --primary-foreground: #ffffff;
  --secondary: #f5f1e8;
  --secondary-foreground: #3c2e26;
  --muted: #f5f1e8;
  --muted-foreground: #8b7355;
  --accent: #f5f1e8;
  --accent-foreground: #3c2e26;
  --border: #e7dcc6;
}

/* Glass theme */
[data-theme="glass"] {
  --background: #f8fafc;
  --foreground: #1e293b;
  --card: rgba(255, 255, 255, 0.7);
  --card-foreground: #1e293b;
  --primary: #06b6d4;
  --primary-foreground: #ffffff;
  --secondary: rgba(241, 245, 249, 0.8);
  --secondary-foreground: #0f172a;
  --muted: rgba(241, 245, 249, 0.8);
  --muted-foreground: #64748b;
  --accent: rgba(241, 245, 249, 0.8);
  --accent-foreground: #0f172a;
  --border: rgba(226, 232, 240, 0.5);
}

/* Dark theme */
[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1a1a1a;
  --card-foreground: #ededed;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #262626;
  --secondary-foreground: #ededed;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --accent: #262626;
  --accent-foreground: #ededed;
  --border: #404040;
}

/* Brand theme */
[data-theme="brand"] {
  --background: #0f0f23;
  --foreground: #ffffff;
  --card: #1a1a3e;
  --card-foreground: #ffffff;
  --primary: #6366f1;
  --primary-foreground: #ffffff;
  --secondary: #312e81;
  --secondary-foreground: #ffffff;
  --muted: #312e81;
  --muted-foreground: #c7d2fe;
  --accent: #312e81;
  --accent-foreground: #ffffff;
  --border: #4338ca;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: system-ui, -apple-system, sans-serif;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Glass morphism effects */
[data-theme="glass"] .bg-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
