import { NextRequest, NextResponse } from 'next/server';
import JSZip from 'jszip';

interface DownloadItem {
  url: string;
  filename: string;
}

export async function POST(request: NextRequest) {
  try {
    const { items }: { items: DownloadItem[] } = await request.json();

    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json({ error: 'Items array is required' }, { status: 400 });
    }

    const zip = new JSZip();
    const downloadPromises = items.map(async (item, index) => {
      try {
        const response = await fetch(item.url);
        if (!response.ok) {
          console.warn(`Failed to fetch ${item.url}: ${response.status}`);
          return null;
        }

        const buffer = await response.arrayBuffer();
        const filename = item.filename || `file_${index + 1}`;
        
        // Add file to zip
        zip.file(filename, buffer);
        return filename;
      } catch (error) {
        console.error(`Error downloading ${item.url}:`, error);
        return null;
      }
    });

    // Wait for all downloads to complete
    const results = await Promise.all(downloadPromises);
    const successCount = results.filter(Boolean).length;

    if (successCount === 0) {
      return NextResponse.json(
        { error: 'No files could be downloaded' },
        { status: 400 }
      );
    }

    // Generate zip file
    const zipBuffer = await zip.generateAsync({ type: 'arraybuffer' });

    // Return zip file
    const headers = new Headers();
    headers.set('Content-Type', 'application/zip');
    headers.set('Content-Disposition', 'attachment; filename="documents.zip"');
    headers.set('Content-Length', zipBuffer.byteLength.toString());

    return new NextResponse(zipBuffer, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('Batch download error:', error);
    return NextResponse.json(
      { error: 'Failed to create batch download' },
      { status: 500 }
    );
  }
}
