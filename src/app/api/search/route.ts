import { NextRequest, NextResponse } from 'next/server';
import { SearchParams, SearchResult } from '@/types/search';

// Mock data for development
const MOCK_RESULTS: SearchResult[] = [
  {
    id: '1',
    title: '智慧民航数据治理规范 数据治理技术',
    url: 'https://example.com/doc1.pdf',
    sourceName: '中国民用航空局',
    sourceIcon: '',
    snippet: '本规范规定了智慧民航数据治理的基本原则、技术要求和实施方法，旨在推进民航行业数据治理体系建设，提升数据质量和数据价值...',
    publishedAt: '2024-01-15',
    fileType: 'pdf',
    fileSize: '2.5MB',
    directDownloadUrl: 'https://example.com/download/doc1.pdf'
  },
  {
    id: '2',
    title: '民航数字化转型指导意见',
    url: 'https://example.com/doc2.docx',
    sourceName: '民航局',
    sourceIcon: '',
    snippet: '为推进民航数字化转型，提升行业治理能力和服务水平，现提出以下指导意见。数字化转型是民航高质量发展的必然要求...',
    publishedAt: '2024-02-20',
    fileType: 'docx',
    fileSize: '1.8MB',
    directDownloadUrl: 'https://example.com/download/doc2.docx'
  },
  {
    id: '3',
    title: '航空安全管理体系建设指南',
    url: 'https://example.com/doc3.pptx',
    sourceName: '中国民航科学技术研究院',
    sourceIcon: '',
    snippet: '航空安全管理体系（SMS）是确保航空安全的重要制度安排。本指南详细介绍了SMS的构建原则、实施步骤和管理要求...',
    publishedAt: '2024-03-10',
    fileType: 'pptx',
    fileSize: '5.2MB',
    directDownloadUrl: 'https://example.com/download/doc3.pptx'
  },
  {
    id: '4',
    title: '民航绿色发展政策解读',
    url: 'https://mp.weixin.qq.com/s/example',
    sourceName: '民航资源网',
    sourceIcon: '',
    snippet: '随着全球气候变化问题日益严峻，民航业绿色发展已成为行业共识。本文深入解读了最新的民航绿色发展政策，分析了政策背景...',
    publishedAt: '2024-03-25',
    fileType: 'article',
    fileSize: undefined,
    directDownloadUrl: undefined
  },
  {
    id: '5',
    title: '机场运营效率提升研究报告',
    url: 'https://example.com/doc5.pdf',
    sourceName: '中国机场协会',
    sourceIcon: '',
    snippet: '本报告基于全国主要机场的运营数据，深入分析了影响机场运营效率的关键因素，提出了提升机场运营效率的策略建议...',
    publishedAt: '2024-04-05',
    fileType: 'pdf',
    fileSize: '3.7MB',
    directDownloadUrl: 'https://example.com/download/doc5.pdf'
  }
];

async function callBaiduAPI(searchParams: SearchParams): Promise<SearchResult[]> {
  const endpoint = process.env.BAIDU_API_ENDPOINT;
  const apiKey = process.env.BAIDU_API_KEY;

  if (!endpoint || !apiKey) {
    console.warn('Baidu API not configured, using mock data');
    return MOCK_RESULTS;
  }

  try {
    // Build search query with filters
    let query = searchParams.query;

    // Add site filters using site: operator
    if (searchParams.sites.length > 0) {
      const siteMap: Record<string, string> = {
        'weixin': 'weixin.qq.com',
        'zhihu': 'zhihu.com',
        'zhishixingqiu': 'zsxq.com',
        'csdn': 'csdn.net',
        'jianshu': 'jianshu.com',
        'segmentfault': 'segmentfault.com',
        'juejin': 'juejin.cn'
      };

      const siteFilters = searchParams.sites
        .map(siteId => siteMap[siteId] || siteId)
        .map(domain => `site:${domain}`)
        .join(' OR ');
      query += ` (${siteFilters})`;
    }

    // Add filetype filters using filetype: operator
    if (searchParams.filetypes.length > 0) {
      const typeMap: Record<string, string[]> = {
        'pdf': ['pdf'],
        'word': ['doc', 'docx'],
        'powerpoint': ['ppt', 'pptx'],
        'article': [] // Articles don't use filetype filter
      };

      const allTypes = searchParams.filetypes
        .filter(type => type !== 'article')
        .flatMap(type => typeMap[type] || [type]);

      if (allTypes.length > 0) {
        const typeFilters = allTypes.map(ext => `filetype:${ext}`).join(' OR ');
        query += ` (${typeFilters})`;
      }
    }

    const url = `${endpoint}?api_key=Bearer+${encodeURIComponent(apiKey)}`;

    // Log the actual parameters being sent to Baidu API
    const requestParams = {
      query,
      originalQuery: searchParams.query,
      sites: searchParams.sites,
      filetypes: searchParams.filetypes,
      page: searchParams.page,
      pageSize: searchParams.pageSize,
      sort: searchParams.sort
    };

    console.log('🔍 Calling Baidu AppBuilder API with parameters:', requestParams);

    // Try different request methods for Baidu AppBuilder API
    // First try GET with query parameters
    const getUrl = `${url}&query=${encodeURIComponent(query)}&stream=false`;

    console.log('🌐 Trying GET request to:', getUrl);

    let response = await fetch(getUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });

    // If GET fails, try POST with different content types
    if (!response.ok && response.status === 405) {
      console.log('🔄 GET failed, trying POST with form data...');

      const formData = new FormData();
      formData.append('query', query);
      formData.append('stream', 'false');
      formData.append('conversation_id', `search_${Date.now()}`);

      response = await fetch(url, {
        method: 'POST',
        body: formData
      });
    }

    // If form data fails, try POST with JSON
    if (!response.ok && response.status === 405) {
      console.log('🔄 Form data failed, trying POST with JSON...');

      const requestBody = {
        query: query,
        stream: false,
        conversation_id: `search_${Date.now()}`,
      };

      response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });
    }

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Baidu API error ${response.status}:`, errorText);
      throw new Error(`Baidu API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('📥 Baidu API response:', data);

    // Transform Baidu API response to our format
    const transformedResults = transformBaiduResponse(data, searchParams);

    console.log('✅ Transformed results:', transformedResults.length, 'items');
    return transformedResults;

  } catch (error) {
    console.error('❌ Baidu API call failed:', error);
    console.log('🔄 Falling back to mock data');
    // Fallback to mock data
    return MOCK_RESULTS;
  }
}

function transformBaiduResponse(baiduData: any, searchParams: SearchParams): SearchResult[] {
  try {
    // The exact structure depends on Baidu AppBuilder API response format
    // This is a best-guess implementation that should be adjusted based on actual API response

    if (!baiduData || !baiduData.result) {
      console.warn('Unexpected Baidu API response structure:', baiduData);
      return MOCK_RESULTS;
    }

    const results: SearchResult[] = [];
    const content = baiduData.result.content || baiduData.result.answer || '';

    // If the response contains structured search results, parse them
    // Otherwise, fall back to mock data with the search query context
    if (typeof content === 'string' && content.includes('http')) {
      // Try to extract URLs and titles from the response text
      const urlRegex = /https?:\/\/[^\s]+/g;
      const urls = content.match(urlRegex) || [];

      urls.forEach((url, index) => {
        // Determine file type from URL
        let fileType: SearchResult['fileType'] = 'article';
        if (url.includes('.pdf')) fileType = 'pdf';
        else if (url.includes('.doc')) fileType = 'doc';
        else if (url.includes('.docx')) fileType = 'docx';
        else if (url.includes('.ppt')) fileType = 'ppt';
        else if (url.includes('.pptx')) fileType = 'pptx';

        // Extract domain for source name
        const domain = new URL(url).hostname;
        let sourceName = domain;
        if (domain.includes('weixin.qq.com')) sourceName = '微信公众号';
        else if (domain.includes('zhihu.com')) sourceName = '知乎';
        else if (domain.includes('csdn.net')) sourceName = 'CSDN';

        results.push({
          id: `baidu_${index + 1}`,
          title: `${searchParams.query} - 搜索结果 ${index + 1}`,
          url,
          sourceName,
          snippet: content.substring(0, 200) + '...',
          publishedAt: new Date().toISOString().split('T')[0],
          fileType,
          directDownloadUrl: fileType !== 'article' ? url : undefined
        });
      });
    }

    // If no structured results found, return enhanced mock data
    if (results.length === 0) {
      return MOCK_RESULTS.map(result => ({
        ...result,
        title: result.title.replace('智慧民航', searchParams.query),
        snippet: result.snippet.replace('智慧民航', searchParams.query)
      }));
    }

    return results;

  } catch (error) {
    console.error('Error transforming Baidu response:', error);
    return MOCK_RESULTS;
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams: urlParams } = new URL(request.url);
    
    const searchParams: SearchParams = {
      query: urlParams.get('query') || '',
      sites: urlParams.get('sites')?.split(',').filter(Boolean) || [],
      filetypes: urlParams.get('filetypes')?.split(',').filter(Boolean) || [],
      page: parseInt(urlParams.get('page') || '1'),
      pageSize: parseInt(urlParams.get('pageSize') || '20'),
      sort: (urlParams.get('sort') as 'relevance' | 'time') || 'relevance',
      timeRange: urlParams.get('timeRange') || undefined
    };

    if (!searchParams.query.trim()) {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 });
    }

    const results = await callBaiduAPI(searchParams);
    
    // Filter results based on file types if specified
    let filteredResults = results;
    if (searchParams.filetypes.length > 0) {
      filteredResults = results.filter(result => 
        searchParams.filetypes.includes(result.fileType)
      );
    }

    return NextResponse.json({
      results: filteredResults,
      total: filteredResults.length,
      page: searchParams.page,
      pageSize: searchParams.pageSize,
      hasMore: false // TODO: Implement proper pagination
    });

  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
