'use client';

import { useState } from 'react';

export default function TestAPI() {
  const [query, setQuery] = useState('智慧民航');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testSearch = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const params = new URLSearchParams({
        query,
        sites: 'weixin,zhihu',
        filetypes: 'pdf,word',
        page: '1',
        pageSize: '5',
        sort: 'relevance'
      });

      const response = await fetch(`/api/search?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'API call failed');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">API 测试页面</h1>
      
      <div className="space-y-4 mb-8">
        <div>
          <label className="block text-sm font-medium mb-2">搜索关键词:</label>
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg"
            placeholder="输入搜索关键词"
          />
        </div>
        
        <button
          onClick={testSearch}
          disabled={loading || !query.trim()}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? '搜索中...' : '测试搜索 API'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>错误:</strong> {error}
        </div>
      )}

      {result && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">API 响应结果:</h2>
          <div className="bg-gray-100 p-4 rounded-lg">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
          
          {result.results && result.results.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-4">搜索结果 ({result.results.length} 项):</h3>
              <div className="space-y-3">
                {result.results.map((item: any, index: number) => (
                  <div key={index} className="border border-gray-200 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-600">{item.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{item.snippet}</p>
                    <div className="flex gap-4 text-xs text-gray-500 mt-2">
                      <span>来源: {item.sourceName}</span>
                      <span>类型: {item.fileType}</span>
                      <span>时间: {item.publishedAt}</span>
                      {item.fileSize && <span>大小: {item.fileSize}</span>}
                    </div>
                    <a 
                      href={item.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline text-sm mt-2 inline-block"
                    >
                      查看原文 →
                    </a>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
      
      <div className="mt-8 pt-8 border-t">
        <a 
          href="/" 
          className="text-blue-600 hover:underline"
        >
          ← 返回主页
        </a>
      </div>
    </div>
  );
}
